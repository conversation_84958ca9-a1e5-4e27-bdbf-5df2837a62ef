import pandas as pd 
import sklearn import dataset
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
from sklearn.metrics import confusion_matrix
from sklearn.metrics import classification_report
from sklearn.metrics import roc_auc_score
from sklearn.metrics import roc_curve
from sklearn.metrics import precision_recall_curve
from sklearn.metrics import f1_score
from sklearn.metrics import auc
from sklearn.metrics import average_precision_score
from sklearn.metrics import precision_score
from sklearn.metrics import recall_score

import mlflow


mlflow.set_tracking_uri("http://localhost:5000")


with mlflow.start_run():
    mlflow.log_metric("test",1)
    mlflow.log_metric("Tai",2)


with mlflow.start_run():
    mlflow.log_metric("test1",1)
    mlflow.log_metric("Tai1",2)

